package main

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
	"github.com/glebarez/sqlite" // 使用纯Go实现的SQLite驱动
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func NewDB() *gorm.DB {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		panic(fmt.Sprintf("获取当前工作目录失败: %v", err))
	}

	// 构建数据库文件的绝对路径
	dbPath := filepath.Join(currentDir, "gorm_test.db")
	fmt.Printf("数据库路径: %s\n", dbPath)

	// 创建GORM配置
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info), // 设置为Info级别以查看SQL语句
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), config)
	if err != nil {
		panic(fmt.Sprintf("连接数据库失败: %v", err))
	}

	fmt.Println("数据库连接成功")

	// 自动迁移表结构
	err = db.AutoMigrate(&Shelf{}, &Book{})
	if err != nil {
		panic(fmt.Sprintf("自动迁移表结构失败: %v", err))
	}

	return db
}

type Shelf struct {
	ID         uint      `gorm:"primaryKey"`
	Type       string    `gorm:"not null"`
	Books      []Book    `gorm:"foreignKey:ShelfID"`
	CreateTime time.Time `gorm:"autoCreateTime"`
	UpdateTime time.Time `gorm:"autoUpdateTime"`
}

type Book struct {
	ID         uint      `gorm:"primaryKey"`
	Title      string    `gorm:"not null"`
	Author     string    `gorm:"not null"`
	ShelfID    uint      `gorm:"not null"`
	CreateTime time.Time `gorm:"autoCreateTime"`
	UpdateTime time.Time `gorm:"autoUpdateTime"`
}
